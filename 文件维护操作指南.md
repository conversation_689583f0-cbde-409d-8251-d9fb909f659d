# 文件维护操作指南

## 维护总览

### 文件维护的重要性
文件维护是长篇小说创作的核心环节，确保：
- **信息一致性**：所有文档信息保持同步更新
- **创作连贯性**：避免前后矛盾和遗漏
- **质量控制**：及时发现和解决问题
- **效率提升**：为后续创作提供准确参考

### 维护频率总表
| 文件类型 | 维护频率 | 触发条件 |
|---------|---------|---------|
| 创作进度.md | 每章完成后 | 章节完成 |
| 情节发展记录.md | 每章完成后 | 情节推进 |
| 章纲详细.md | 每章前后 | 章节创作 |
| 人物档案.md | 人物发展时 | 重要变化 |
| 人物关系图谱.md | 关系变化时 | 互动发生 |
| 人物成长规划.md | 成长节点时 | 达成里程碑 |
| 卷纲规划.md | 每10章/卷末 | 阶段完成 |
| 主线大纲.md | 重要转折后 | 方向调整 |
| 世界观设定.md | 设定变化时 | 新增设定 |
| 势力关系.md | 关系变化时 | 势力发展 |
| 小说基本信息.md | 重大调整时 | 策略变更 |
| 待解决问题.md | 发现/解决时 | 问题管理 |

## 具体操作指南

### 每章创作后的标准维护流程

#### 步骤1：创作进度.md 更新
```markdown
## 操作步骤
1. 打开 04-创作记录/创作进度.md
2. 在"最近更新记录"部分添加新条目：

### 第X章 - [章节标题]
- 创作日期：[今天日期]
- 字数：[实际字数]
- 创作时长：[花费时间]
- 质量自评：[优秀/良好/一般]
- 读者反馈：[积极/中性/需改进]
- 关键情节：[本章主要情节，1-2句话]
- 遗留问题：[需要注意的问题]
- 下章规划：[下一章重点内容]

3. 更新"整体进度概览"：
- 当前章：第X章
- 总字数：[累计字数]
- 完成度：[百分比]
- 最后更新：[今天日期]

4. 更新"数据统计"部分的相关数据
```

#### 步骤2：情节发展记录.md 更新
```markdown
## 操作步骤
1. 打开 04-创作记录/情节发展记录.md
2. 更新"主线发展状态"（如有推进）
3. 更新相关"故事线状态"：

### [故事线名称] - 第X章更新
- 本章发展：[具体描述本章在这条线上的发展]
- 当前状态：[更新后的状态]
- 完成度：[百分比]
- 下一步发展：[下一步的规划]

4. 在"重要情节节点"部分添加：

### 第X章 - [章节标题]
- 关键事件：[本章的关键事件]
- 影响：[对故事的影响]
- 后续影响：[可能的后续影响]
```

#### 步骤3：章纲详细.md 状态更新
```markdown
## 操作步骤
1. 打开 02-故事架构/章纲详细.md
2. 找到第X章的条目
3. 将状态标记从 🔄 或 📋 改为 ✅
4. 如果实际创作与规划有差异，添加备注：

#### 第X章：[标题] ✅
- **实际创作与规划差异**：[说明差异]
- **调整原因**：[解释原因]
- **对后续影响**：[分析影响]

5. 检查并调整后续章节的规划（如需要）
```

#### 步骤4：人物相关文件维护（按需）
```markdown
## 人物档案更新（如有人物重要发展）
1. 打开 03-人物系统/主要人物档案.md
2. 找到相关人物条目
3. 在人物档案末尾添加更新记录：

### [人物名] 状态更新 - 第X章
- 修为变化：[如有变化]
- 重要经历：[本章的重要经历]
- 性格发展：[性格方面的变化]
- 关系变化：[与其他人物关系的变化]
- 下一步发展：[预期的发展方向]

## 人物关系更新（如有重要互动）
1. 打开 03-人物系统/人物关系图谱.md
2. 找到相关关系条目
3. 更新关系强度和状态：

### [人物A] ↔ [人物B] - 第X章更新
- 关系变化：从 ★★★☆☆ → ★★★★☆
- 变化原因：[具体事件]
- 影响分析：[对故事的影响]
- 未来发展：[预期变化]

## 人物成长跟踪（如达成成长节点）
1. 打开 03-人物系统/人物成长规划.md
2. 标记完成的成长节点：

### [人物名] 成长节点达成 - 第X章
- 达成节点：[具体的成长节点]
- 表现形式：[如何体现的成长]
- 影响分析：[对人物和故事的影响]
- 下一节点：[下一个成长目标]
```

#### 步骤5：问题管理
```markdown
## 操作步骤
1. 打开 04-创作记录/待解决问题.md
2. 如发现新问题，添加到相应优先级分类：

#### 问题[编号]：[问题标题]
- 问题描述：[详细描述]
- 发现章节：第X章
- 影响范围：[影响分析]
- 紧急程度：[高/中/低]
- 解决方案：[初步想法]
- 预计解决时间：[时间规划]
- 当前状态：待处理

3. 如解决了现有问题，更新状态：
- 将问题从"当前问题"移动到"已解决问题"
- 添加解决方案和效果评估
```

### 定期维护操作

#### 每10章的深度维护
```markdown
## 操作清单
1. 【卷纲进度检查】
   - 打开 02-故事架构/卷纲规划.md
   - 更新当前卷的完成进度
   - 分析是否需要调整后续规划
   - 评估节奏和质量

2. 【整体进度分析】
   - 分析创作效率趋势
   - 检查读者反馈变化
   - 评估故事发展是否符合预期
   - 制定调整措施

3. 【文档整理优化】
   - 清理过时信息
   - 整合分散的相关信息
   - 优化文档结构
   - 备份重要版本
```

#### 每卷完成后的全面维护
```markdown
## 操作清单
1. 【全面回顾总结】
   - 分析本卷的成功和不足
   - 总结读者反馈和数据表现
   - 评估人物和情节发展效果
   - 制定下一卷改进计划

2. 【架构文件更新】
   - 更新 02-故事架构/主线大纲.md
   - 调整 02-故事架构/卷纲规划.md
   - 完善 02-故事架构/章纲详细.md

3. 【人物系统完善】
   - 全面更新人物档案
   - 完善人物关系图谱
   - 调整人物成长规划

4. 【系统维护】
   - 备份所有重要文档
   - 优化工作流程
   - 总结创作经验
   - 准备下一卷环境
```

## 维护质量控制

### 维护检查标准
- **完整性**：所有相关文件都已更新
- **准确性**：信息准确无误，无前后矛盾
- **及时性**：在规定时间内完成更新
- **有用性**：更新的信息对后续创作有指导价值

### 常见维护错误
1. **遗漏更新**：忘记更新某些相关文件
2. **信息不一致**：不同文件中的信息出现矛盾
3. **更新不及时**：拖延更新导致信息过时
4. **记录不详细**：更新内容过于简略，缺乏指导价值

### 维护效率提升
1. **建立习惯**：每章完成后立即进行维护
2. **使用模板**：使用标准化的更新模板
3. **批量处理**：将相关的更新操作集中处理
4. **定期检查**：定期检查维护质量和完整性

## 维护工具和技巧

### 快速定位技巧
- 使用文档内搜索功能快速找到需要更新的内容
- 建立统一的标记系统（如日期标记、状态标记）
- 使用目录和索引快速导航

### 批量更新技巧
- 准备更新内容清单，避免遗漏
- 按文件类型分组进行更新
- 使用复制粘贴减少重复输入

### 质量保证技巧
- 更新完成后进行交叉检查
- 定期回顾更新内容的准确性
- 建立更新日志，跟踪维护历史

---
*本指南会根据使用经验持续优化*
*最后更新：[日期]*
