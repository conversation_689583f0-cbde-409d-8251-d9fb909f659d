# 长篇网络小说智能体使用指南

## 系统概述
这个智能体系统分为两个阶段：
1. **初始化阶段**：使用 `小说智能体-初始化.md` 规则，帮助建立完整的小说基础设定
2. **创作阶段**：使用 `小说智能体-创作.md` 规则，进行日常的章节创作和文件维护

## 使用流程

### 第一步：初始化（首次使用）
1. 告诉 Augment 使用初始化规则：
   ```
   请按照"小说智能体-初始化.md"中的规则，帮我创建一个新的长篇小说项目
   ```

2. 系统会引导您完成：
   - 小说基本信息设定
   - 世界观构建
   - 故事架构设计
   - 人物系统建立
   - 工作文件夹创建

### 第二步：日常创作（初始化完成后）
1. 每次创作时告诉 Augment：
   ```
   请按照"小说智能体-创作.md"中的规则，帮我进行第X章的创作
   ```

2. 系统会自动：
   - 查阅相关工作文件
   - 确认当前进度和状态
   - 指导章节创作
   - 更新维护各种记录

## 文件结构说明

### 规则文件
- `小说智能体-初始化.md` - 初始化阶段的工作规则
- `小说智能体-创作.md` - 创作阶段的工作规则
- `使用指南.md` - 本文件，使用说明

### 项目文件结构（初始化后会创建）
```
小说项目/
├── 01-基础设定/
│   ├── 小说基本信息.md
│   ├── 世界观设定.md
│   └── 势力关系.md
├── 02-故事架构/
│   ├── 主线大纲.md
│   ├── 卷纲规划.md
│   └── 章纲详细.md
├── 03-人物系统/
│   ├── 主要人物档案.md
│   ├── 人物关系图谱.md
│   └── 人物成长规划.md
├── 04-创作记录/
│   ├── 创作进度.md
│   ├── 情节发展记录.md
│   └── 待解决问题.md
└── 05-正文内容/
    ├── 第一卷/
    └── 第二卷/
```

## 使用技巧

### 初始化阶段技巧
1. **准备充分**：在开始前先思考小说的核心创意
2. **循序渐进**：不要急于求成，按系统引导逐步完善
3. **保持开放**：接受系统的建议和启发
4. **详细记录**：确保所有重要信息都被记录

### 创作阶段技巧
1. **定期回顾**：每隔几章回顾一下整体进度
2. **及时更新**：每章完成后立即更新相关文档
3. **问题记录**：发现问题立即记录，不要拖延
4. **灵活调整**：根据创作需要适时调整规划

## 常见问题

### Q: 如果中途想修改设定怎么办？
A: 可以随时修改相关文档，但要确保：
- 评估修改对整体的影响
- 更新所有相关文档
- 检查已写内容是否需要调整

### Q: 如果忘记更新文档怎么办？
A: 系统会提醒您更新，也可以：
- 回顾最近几章的内容
- 补充更新遗漏的记录
- 建立定期检查的习惯

### Q: 如何处理创作灵感与规划冲突？
A: 系统会帮您：
- 评估灵感的价值
- 分析与现有规划的兼容性
- 提供调整建议
- 更新相关文档

## 最佳实践

### 文档管理
1. **命名规范**：使用清晰的文件命名
2. **定期备份**：重要修改前先备份
3. **版本记录**：记录重要修改的原因和时间
4. **交叉检查**：定期检查文档间的一致性

### 创作习惯
1. **固定流程**：每次创作都按相同流程进行
2. **质量优先**：不要为了速度牺牲质量
3. **读者视角**：时常从读者角度审视内容
4. **持续改进**：根据经验不断优化工作方式

## 系统优势

### 结构化管理
- 完整的文档体系
- 清晰的工作流程
- 系统的质量控制

### 连贯性保证
- 自动的一致性检查
- 完整的发展记录
- 及时的问题提醒

### 效率提升
- 减少重复思考
- 避免遗漏重要信息
- 提供创作指导

### 质量保障
- 多层次的质量检查
- 完整的规划体系
- 专业的创作建议

## 开始使用

准备好开始您的长篇小说创作之旅了吗？

1. 如果是第一次使用，请说：
   ```
   请按照"小说智能体-初始化.md"中的规则，帮我创建一个新的长篇小说项目
   ```

2. 如果已经完成初始化，请说：
   ```
   请按照"小说智能体-创作.md"中的规则，帮我进行第X章的创作
   ```

系统会根据您的需求，按照相应的规则为您提供专业的创作支持！
