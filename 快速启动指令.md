# 长篇网络小说智能体 - 快速启动指令

## 系统启动指令

### 🚀 初始化新项目
```
请按照"小说智能体-初始化.md"中的规则，帮我创建一个新的长篇小说项目。

我想写一部[题材类型]小说，大概的想法是[简要描述您的创意]。

请引导我完成完整的初始化流程。
```

### ✍️ 开始日常创作
```
请按照"小说智能体-创作.md"中的规则，帮我进行第[X]章的创作。

请先查阅相关工作文件，确认当前进度和状态，然后指导我完成本章的创作。
```

### 📋 检查项目状态
```
请按照"小说智能体-创作.md"中的规则，帮我检查当前项目的整体状态。

请查阅所有相关工作文件，给我一个完整的项目状态报告。
```

### 🔧 修改项目设定
```
请按照相应的规则，帮我修改项目中的[具体设定内容]。

请评估修改的影响，并更新所有相关文档。
```

## 常用对话模板

### 初始化阶段常用语句

#### 开始初始化
- "我想创建一个新的长篇小说项目，请帮我开始初始化流程"
- "我有一个小说创意，想要系统化地开发成长篇作品"
- "请引导我建立完整的小说世界观和故事架构"

#### 提供基本信息
- "我想写[题材]类型的小说，主要讲述[简要剧情]"
- "目标读者是[读者群体]，预计写[字数规模]"
- "我的核心创意是[独特设定或卖点]"

#### 完善设定
- "我需要帮助完善世界观设定"
- "请帮我设计主要人物的关系网络"
- "我想要制定详细的故事大纲"

### 创作阶段常用语句

#### 开始创作
- "我要开始写第[X]章了，请帮我准备"
- "请检查我当前的创作进度和状态"
- "我需要确认第[X]章应该写什么内容"

#### 创作过程中
- "这个情节发展是否合理？"
- "这个人物的行为是否符合设定？"
- "我想在这里埋一个伏笔，应该怎么处理？"

#### 创作完成后
- "第[X]章写完了，请帮我更新相关记录"
- "请检查这一章是否有需要注意的问题"
- "请更新人物状态和情节发展记录"

### 问题处理常用语句

#### 发现问题
- "我发现了一个设定矛盾，需要处理"
- "这个情节和之前的内容有冲突"
- "我想修改某个人物的设定"

#### 寻求建议
- "我遇到了创作瓶颈，请给我一些建议"
- "这个情节应该如何发展比较好？"
- "我应该如何处理这个人物关系？"

#### 调整规划
- "我想调整故事的发展方向"
- "我需要修改章纲规划"
- "我想增加一个新的重要角色"

## 高效使用技巧

### 💡 提高效率的方法

1. **明确指令**
   - 清楚说明您的需求
   - 提供必要的背景信息
   - 指定需要使用的规则文件

2. **充分准备**
   - 在开始前整理好思路
   - 准备好相关的创意素材
   - 明确当前的创作目标

3. **及时反馈**
   - 对系统的建议及时回应
   - 说明您的想法和偏好
   - 指出不满意的地方

### 🎯 获得最佳效果的建议

1. **详细描述**
   - 提供丰富的细节信息
   - 说明您的创作风格偏好
   - 描述目标读者的特点

2. **积极互动**
   - 主动提出问题和想法
   - 参与讨论和头脑风暴
   - 接受建设性的建议

3. **保持一致**
   - 坚持使用系统的工作流程
   - 及时更新和维护文档
   - 保持创作的连续性

## 故障排除

### 🔍 常见问题及解决方案

#### 系统没有按规则工作
**解决方案**：
- 明确指定要使用的规则文件
- 重新发送完整的启动指令
- 检查指令格式是否正确

#### 文档更新不及时
**解决方案**：
- 主动提醒系统更新文档
- 检查是否遗漏了某些步骤
- 定期要求系统检查文档状态

#### 创作建议不符合预期
**解决方案**：
- 提供更详细的需求描述
- 说明您的具体偏好
- 要求系统重新分析和建议

### 🆘 紧急情况处理

#### 项目文件丢失或损坏
1. 立即停止当前工作
2. 检查是否有备份文件
3. 根据现有信息重建关键文档
4. 建立更好的备份机制

#### 创作方向严重偏离
1. 回顾原始的项目规划
2. 分析偏离的原因和程度
3. 决定是调整规划还是回到正轨
4. 更新所有相关文档

## 进阶使用

### 🚀 高级功能

1. **批量处理**
   - 一次性更新多个文档
   - 批量检查多个章节
   - 统一调整多个设定

2. **深度分析**
   - 分析整体创作质量
   - 评估读者反馈趋势
   - 预测发展方向

3. **创新实验**
   - 尝试新的创作技巧
   - 实验不同的情节发展
   - 测试新的人物设定

### 📈 持续改进

1. **定期回顾**
   - 每周回顾创作进展
   - 每月评估整体质量
   - 每季度调整长期规划

2. **学习提升**
   - 分析成功案例
   - 学习新的创作技巧
   - 参考读者反馈改进

3. **系统优化**
   - 根据使用经验调整流程
   - 完善文档模板
   - 提高工作效率

---

## 🎉 开始您的创作之旅

准备好了吗？选择一个启动指令，开始您的长篇小说创作之旅吧！

记住：好的开始是成功的一半，系统化的方法会让您的创作更加高效和专业！
