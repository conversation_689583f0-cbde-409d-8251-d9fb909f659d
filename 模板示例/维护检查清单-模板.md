# 维护检查清单模板

## 每章创作后维护清单

### 第[X]章维护记录 - [日期]

#### ✅ 基础记录更新
- [ ] **创作进度.md 已更新**
  - [ ] 添加本章完成记录（日期、字数、时长）
  - [ ] 更新质量自评和读者反馈
  - [ ] 记录关键情节和遗留问题
  - [ ] 规划下一章重点内容
  - [ ] 更新整体进度统计

- [ ] **情节发展记录.md 已更新**
  - [ ] 记录本章推进的主要情节
  - [ ] 更新相关故事线状态
  - [ ] 标记完成的情节节点
  - [ ] 分析情节发展节奏

#### ✅ 架构文件维护
- [ ] **章纲详细.md 状态更新**
  - [ ] 标记本章为已完成状态（✅）
  - [ ] 记录与原规划的差异（如有）
  - [ ] 调整后续章节规划（如需要）

- [ ] **卷纲规划.md 检查**（每10章或重要节点）
  - [ ] 更新当前卷完成进度
  - [ ] 评估是否需要调整规划
  - [ ] 为下一阶段做准备

- [ ] **主线大纲.md 检查**（重要转折点后）
  - [ ] 确认故事发展符合主线
  - [ ] 评估是否需要调整方向
  - [ ] 更新转折点安排

#### ✅ 人物系统维护（按需）
- [ ] **人物档案.md 更新**（如有人物重要发展）
  - [ ] 更新人物修为、状态变化
  - [ ] 记录人物重要经历
  - [ ] 调整性格发展描述
  - [ ] 涉及人物：[列出相关人物]

- [ ] **人物关系图谱.md 更新**（如有重要互动）
  - [ ] 记录人物关系变化
  - [ ] 更新关系强度评级
  - [ ] 分析关系发展影响
  - [ ] 涉及关系：[列出相关关系]

- [ ] **人物成长规划.md 检查**（如达成成长节点）
  - [ ] 标记达成的成长节点
  - [ ] 调整后续成长规划
  - [ ] 分析成长对故事的推动
  - [ ] 涉及人物：[列出相关人物]

#### ✅ 基础设定维护（按需）
- [ ] **世界观设定.md 更新**（如有新设定）
  - [ ] 添加新的世界观元素
  - [ ] 完善现有设定细节
  - [ ] 确保设定一致性
  - [ ] 新增内容：[列出新增内容]

- [ ] **势力关系.md 更新**（如有势力变化）
  - [ ] 更新势力状态和关系
  - [ ] 记录重要势力事件
  - [ ] 分析对整体格局的影响
  - [ ] 变化内容：[列出变化内容]

#### ✅ 问题管理
- [ ] **待解决问题.md 更新**
  - [ ] 记录新发现的问题
  - [ ] 更新现有问题状态
  - [ ] 分析问题优先级
  - [ ] 制定解决时间表

- [ ] **伏笔线索管理**
  - [ ] 记录新埋设的伏笔
  - [ ] 标记已揭示的伏笔
  - [ ] 检查遗漏的线索
  - [ ] 规划后续揭示时机

### 维护完成确认
- [ ] **所有相关文件已更新**
- [ ] **信息一致性已检查**
- [ ] **下一章准备工作已完成**
- [ ] **维护质量自评**：优秀/良好/需改进

### 维护备注
```
[记录维护过程中的特殊情况、发现的问题、改进建议等]
```

---

## 每10章深度维护清单

### 第[X-X]章阶段维护记录 - [日期]

#### ✅ 整体进度评估
- [ ] **创作效率分析**
  - [ ] 统计最近10章的创作数据
  - [ ] 分析效率变化趋势
  - [ ] 识别影响效率的因素
  - [ ] 制定效率改进措施

- [ ] **质量趋势分析**
  - [ ] 评估最近10章的质量水平
  - [ ] 分析读者反馈变化
  - [ ] 识别质量问题和亮点
  - [ ] 制定质量提升计划

- [ ] **进度符合度检查**
  - [ ] 对比实际进度与计划进度
  - [ ] 分析进度偏差原因
  - [ ] 评估对整体规划的影响
  - [ ] 调整后续进度安排

#### ✅ 故事发展评估
- [ ] **情节发展分析**
  - [ ] 评估各条故事线的发展状况
  - [ ] 分析情节节奏是否合适
  - [ ] 检查是否有遗漏的线索
  - [ ] 规划下一阶段的情节发展

- [ ] **人物发展分析**
  - [ ] 评估主要人物的成长轨迹
  - [ ] 分析人物关系的发展状况
  - [ ] 检查人物行为的一致性
  - [ ] 规划下一阶段的人物发展

- [ ] **世界观一致性检查**
  - [ ] 检查设定的一致性
  - [ ] 识别可能的矛盾点
  - [ ] 完善不足的设定细节
  - [ ] 确保世界观的完整性

#### ✅ 文档整理优化
- [ ] **信息整合**
  - [ ] 整合分散的相关信息
  - [ ] 清理过时或冗余内容
  - [ ] 优化文档结构和格式
  - [ ] 建立更好的索引系统

- [ ] **备份管理**
  - [ ] 备份重要文档的当前版本
  - [ ] 清理过期的备份文件
  - [ ] 建立版本控制机制
  - [ ] 确保数据安全

#### ✅ 问题处理回顾
- [ ] **问题解决效果评估**
  - [ ] 回顾已解决问题的效果
  - [ ] 分析解决方案的有效性
  - [ ] 总结问题解决经验
  - [ ] 完善问题预防机制

- [ ] **新问题识别**
  - [ ] 识别潜在的新问题
  - [ ] 评估问题的影响程度
  - [ ] 制定问题解决计划
  - [ ] 建立问题监控机制

### 阶段维护总结
```
[总结本阶段的主要成果、发现的问题、改进措施等]
```

---

## 每卷完成后全面维护清单

### 第[X]卷完成维护记录 - [日期]

#### ✅ 卷级总结回顾
- [ ] **成果总结**
  - [ ] 总结本卷的主要成就
  - [ ] 分析达成的创作目标
  - [ ] 评估读者反馈和市场表现
  - [ ] 记录成功经验和亮点

- [ ] **问题分析**
  - [ ] 识别本卷的主要问题
  - [ ] 分析问题产生的原因
  - [ ] 评估问题的影响程度
  - [ ] 制定改进措施

- [ ] **数据统计**
  - [ ] 统计本卷的各项数据
  - [ ] 分析数据变化趋势
  - [ ] 对比预期目标和实际结果
  - [ ] 为下一卷设定数据目标

#### ✅ 全面文档更新
- [ ] **故事架构完善**
  - [ ] 全面更新主线大纲
  - [ ] 完善卷纲规划
  - [ ] 调整章纲详细规划
  - [ ] 确保架构的一致性

- [ ] **人物系统完善**
  - [ ] 全面更新人物档案
  - [ ] 完善人物关系图谱
  - [ ] 调整人物成长规划
  - [ ] 确保人物发展的合理性

- [ ] **世界观设定完善**
  - [ ] 补充和完善世界观设定
  - [ ] 更新势力关系状况
  - [ ] 确保设定的完整性和一致性
  - [ ] 为下一卷的发展做准备

#### ✅ 系统维护优化
- [ ] **工作流程优化**
  - [ ] 回顾和评估当前工作流程
  - [ ] 识别流程中的问题和瓶颈
  - [ ] 制定流程改进方案
  - [ ] 更新工作标准和规范

- [ ] **工具和模板优化**
  - [ ] 评估现有工具和模板的效果
  - [ ] 根据使用经验进行优化
  - [ ] 开发新的辅助工具
  - [ ] 完善模板库

- [ ] **知识管理**
  - [ ] 整理和归档创作经验
  - [ ] 建立知识库和参考资料
  - [ ] 总结最佳实践
  - [ ] 为团队协作做准备

#### ✅ 下一卷准备
- [ ] **规划制定**
  - [ ] 制定下一卷的详细规划
  - [ ] 设定创作目标和时间表
  - [ ] 准备必要的资源和工具
  - [ ] 建立质量控制机制

- [ ] **环境准备**
  - [ ] 整理和优化创作环境
  - [ ] 准备参考资料和素材
  - [ ] 建立支持系统
  - [ ] 确保创作条件的完备

### 卷级维护总结
```
[总结本卷的整体情况、主要收获、改进方向、下一卷的期望等]
```

---

## 维护质量自评

### 维护完整性评分
- 基础记录更新：[1-5分]
- 架构文件维护：[1-5分]
- 人物系统维护：[1-5分]
- 问题管理：[1-5分]
- 总体评分：[平均分]

### 维护及时性评分
- 更新及时性：[1-5分]
- 信息准确性：[1-5分]
- 一致性保持：[1-5分]
- 总体评分：[平均分]

### 改进建议
```
[记录维护过程中发现的问题和改进建议]
```

---
*维护完成时间：[具体时间]*
*下次维护提醒：[下次维护时间]*
